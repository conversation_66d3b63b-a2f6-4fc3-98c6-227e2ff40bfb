{"name": "copy-webpack-plugin", "version": "13.0.0", "description": "Copy files && directories with webpack", "license": "MIT", "repository": "webpack-contrib/copy-webpack-plugin", "author": "<PERSON>", "homepage": "https://github.com/webpack-contrib/copy-webpack-plugin", "bugs": "https://github.com/webpack-contrib/copy-webpack-plugin/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist types", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types --rootDir src && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky && npm run build", "release": "standard-version"}, "files": ["dist", "types"], "peerDependencies": {"webpack": "^5.1.0"}, "dependencies": {"glob-parent": "^6.0.1", "normalize-path": "^3.0.0", "schema-utils": "^4.2.0", "serialize-javascript": "^6.0.2", "tinyglobby": "^0.2.12"}, "devDependencies": {"@babel/cli": "^7.24.6", "@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/preset-env": "^7.25.3", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@types/glob-parent": "^5.1.3", "@types/node": "^22.13.5", "@types/normalize-path": "^3.0.2", "@types/serialize-javascript": "^5.0.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^30.0.0", "cross-env": "^7.0.3", "cspell": "^8.15.6", "del": "^6.1.1", "del-cli": "^6.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "file-loader": "^6.2.0", "husky": "^9.1.4", "is-gzip": "^2.0.0", "jest": "^30.0.0", "lint-staged": "^15.2.8", "memfs": "^4.11.1", "npm-run-all": "^4.1.5", "prettier": "^3.2.5", "standard-version": "^9.3.1", "typescript": "^5.4.5", "webpack": "^5.91.0"}, "keywords": ["webpack", "plugin", "transfer", "move", "copy"]}
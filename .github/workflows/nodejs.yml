name: copy-webpack-plugin

on:
  push:
    branches:
      - master
      - next
  pull_request:
    branches:
      - master
      - next

permissions:
  contents: read

jobs:
  lint:
    name: Lint - ${{ matrix.os }} - Node v${{ matrix.node-version }}

    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    strategy:
      matrix:
        os: [ubuntu-latest]
        node-version: [lts/*]

    runs-on: ${{ matrix.os }}

    concurrency:
      group: lint-${{ matrix.os }}-v${{ matrix.node-version }}-${{ github.ref }}
      cancel-in-progress: true

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Lint
        run: npm run lint

      - name: Build types
        run: npm run build:types

      - name: Check types
        run: if [ -n "$(git status types --porcelain)" ]; then echo "Missing types. Update types by running 'npm run build:types'"; exit 1; else echo "All types are valid"; fi

      - name: Security audit
        run: npm run security

      - name: Validate PR commits with commitlint
        if: github.event_name == 'pull_request'
        run: npx commitlint --from ${{ github.event.pull_request.head.sha }}~${{ github.event.pull_request.commits }} --to ${{ github.event.pull_request.head.sha }} --verbose

  test:
    name: Test - ${{ matrix.os }} - Node v${{ matrix.node-version }}, Webpack ${{ matrix.webpack-version }}

    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: [18.x, 20.x, 22.x, 24.x]
        webpack-version: [latest]

    runs-on: ${{ matrix.os }}

    concurrency:
      group: test-${{ matrix.os }}-v${{ matrix.node-version }}-${{ matrix.webpack-version }}-${{ github.ref }}
      cancel-in-progress: true

    steps:
      - name: Setup Git
        if: matrix.os == 'windows-latest'
        run: git config --global core.autocrlf input

      - uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Install webpack ${{ matrix.webpack-version }}
        if: matrix.webpack-version != 'latest'
        run: npm i webpack@${{ matrix.webpack-version }}

      - name: Run tests for webpack version ${{ matrix.webpack-version }}
        run: npm run test:coverage -- --ci

      - name: Submit coverage data to codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

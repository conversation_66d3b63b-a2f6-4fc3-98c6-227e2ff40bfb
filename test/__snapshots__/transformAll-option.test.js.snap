// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`cache should work with the "memory" cache: assets 1`] = `
{
  "file.txt": "new::directory/nested/deep-nested/deepnested.txt::directory/nested/nestedfile.txt::",
}
`;

exports[`cache should work with the "memory" cache: assets 2`] = `
{
  "file.txt": "new::directory/nested/deep-nested/deepnested.txt::directory/nested/nestedfile.txt::",
}
`;

exports[`cache should work with the "memory" cache: errors 1`] = `[]`;

exports[`cache should work with the "memory" cache: errors 2`] = `[]`;

exports[`cache should work with the "memory" cache: warnings 1`] = `[]`;

exports[`cache should work with the "memory" cache: warnings 2`] = `[]`;

// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CopyPlugin basic should work with multi compiler mode: assets 1`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin basic should work with multi compiler mode: assets 2`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin basic should work with multi compiler mode: errors 1`] = `[]`;

exports[`CopyPlugin basic should work with multi compiler mode: errors 2`] = `[]`;

exports[`CopyPlugin basic should work with multi compiler mode: warnings 1`] = `[]`;

exports[`CopyPlugin basic should work with multi compiler mode: warnings 2`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: assets 1`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: assets 2`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: assets 3`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: assets 4`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: errors 1`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: errors 2`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: errors 3`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: errors 4`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: warnings 1`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: warnings 2`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: warnings 3`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache and multi compiler mode: warnings 4`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache: assets 1`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache: assets 2`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "filesystem" cache: errors 1`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache: errors 2`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache: warnings 1`] = `[]`;

exports[`CopyPlugin cache should work with the "filesystem" cache: warnings 2`] = `[]`;

exports[`CopyPlugin cache should work with the "memory" cache: assets 1`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "memory" cache: assets 2`] = `
{
  ".dottedfile": "dottedfile contents
",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin cache should work with the "memory" cache: errors 1`] = `[]`;

exports[`CopyPlugin cache should work with the "memory" cache: errors 2`] = `[]`;

exports[`CopyPlugin cache should work with the "memory" cache: warnings 1`] = `[]`;

exports[`CopyPlugin cache should work with the "memory" cache: warnings 2`] = `[]`;

exports[`CopyPlugin cache should work with the "transform" option: assets 1`] = `
{
  "new0.txt": "new",
  "new1-2.txt": "newadded1",
  "new1.txt": "newadded1",
  "new2.txt": "newadded2",
  "new3.txt": "newadded3",
  "new4.txt": "newbaz",
  "new5.txt": "newbaz",
  "new6.txt": "newbaz",
}
`;

exports[`CopyPlugin cache should work with the "transform" option: assets 2`] = `
{
  "new0.txt": "new",
  "new1-2.txt": "newadded1",
  "new1.txt": "newadded1",
  "new2.txt": "newadded2",
  "new3.txt": "newadded3",
  "new4.txt": "newbaz",
  "new5.txt": "newbaz",
  "new6.txt": "newbaz",
}
`;

exports[`CopyPlugin cache should work with the "transform" option: errors 1`] = `[]`;

exports[`CopyPlugin cache should work with the "transform" option: errors 2`] = `[]`;

exports[`CopyPlugin cache should work with the "transform" option: warnings 1`] = `[]`;

exports[`CopyPlugin cache should work with the "transform" option: warnings 2`] = `[]`;

exports[`CopyPlugin logging should logging when "from" is a directory: logs 1`] = `
{
  "logs": [
    "'to' option '.' determinated as 'dir'",
    "'to' option '.' determinated as 'dir'",
    "'to' option '.' determinated as 'dir'",
    "'to' option '.' determinated as 'dir'",
    "added './fixtures/directory' as a context dependency",
    "added './fixtures/directory/.dottedfile' as a file dependency",
    "added './fixtures/directory/directoryfile.txt' as a file dependency",
    "added './fixtures/directory/nested/deep-nested/deepnested.txt' as a file dependency",
    "added './fixtures/directory/nested/nestedfile.txt' as a file dependency",
    "begin globbing './fixtures/directory/**/*'...",
    "created snapshot for './fixtures/directory/.dottedfile'",
    "created snapshot for './fixtures/directory/directoryfile.txt'",
    "created snapshot for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "created snapshot for './fixtures/directory/nested/nestedfile.txt'",
    "creating snapshot for './fixtures/directory/.dottedfile'...",
    "creating snapshot for './fixtures/directory/directoryfile.txt'...",
    "creating snapshot for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "creating snapshot for './fixtures/directory/nested/nestedfile.txt'...",
    "determined './fixtures/directory' is a directory",
    "determined that './fixtures/directory/.dottedfile' should write to '.dottedfile'",
    "determined that './fixtures/directory/directoryfile.txt' should write to 'directoryfile.txt'",
    "determined that './fixtures/directory/nested/deep-nested/deepnested.txt' should write to 'nested/deep-nested/deepnested.txt'",
    "determined that './fixtures/directory/nested/nestedfile.txt' should write to 'nested/nestedfile.txt'",
    "finished to adding additional assets",
    "finished to process a pattern from 'directory' using './fixtures/directory' context",
    "found './fixtures/directory/.dottedfile'",
    "found './fixtures/directory/directoryfile.txt'",
    "found './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "found './fixtures/directory/nested/nestedfile.txt'",
    "getting cache for './fixtures/directory/.dottedfile'...",
    "getting cache for './fixtures/directory/directoryfile.txt'...",
    "getting cache for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "getting cache for './fixtures/directory/nested/nestedfile.txt'...",
    "getting stats for './fixtures/directory'...",
    "missed cache for './fixtures/directory/.dottedfile'",
    "missed cache for './fixtures/directory/directoryfile.txt'",
    "missed cache for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "missed cache for './fixtures/directory/nested/nestedfile.txt'",
    "read './fixtures/directory/.dottedfile'",
    "read './fixtures/directory/directoryfile.txt'",
    "read './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "read './fixtures/directory/nested/nestedfile.txt'",
    "reading './fixtures/directory/.dottedfile'...",
    "reading './fixtures/directory/directoryfile.txt'...",
    "reading './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "reading './fixtures/directory/nested/nestedfile.txt'...",
    "starting to add additional assets...",
    "starting to process a pattern from 'directory' using './fixtures' context",
    "stored cache for './fixtures/directory/.dottedfile'",
    "stored cache for './fixtures/directory/directoryfile.txt'",
    "stored cache for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "stored cache for './fixtures/directory/nested/nestedfile.txt'",
    "storing cache for './fixtures/directory/.dottedfile'...",
    "storing cache for './fixtures/directory/directoryfile.txt'...",
    "storing cache for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "storing cache for './fixtures/directory/nested/nestedfile.txt'...",
    "writing '.dottedfile' from './fixtures/directory/.dottedfile' to compilation assets...",
    "writing 'directoryfile.txt' from './fixtures/directory/directoryfile.txt' to compilation assets...",
    "writing 'nested/deep-nested/deepnested.txt' from './fixtures/directory/nested/deep-nested/deepnested.txt' to compilation assets...",
    "writing 'nested/nestedfile.txt' from './fixtures/directory/nested/nestedfile.txt' to compilation assets...",
    "written '.dottedfile' from './fixtures/directory/.dottedfile' to compilation assets",
    "written 'directoryfile.txt' from './fixtures/directory/directoryfile.txt' to compilation assets",
    "written 'nested/deep-nested/deepnested.txt' from './fixtures/directory/nested/deep-nested/deepnested.txt' to compilation assets",
    "written 'nested/nestedfile.txt' from './fixtures/directory/nested/nestedfile.txt' to compilation assets",
  ],
}
`;

exports[`CopyPlugin logging should logging when "from" is a file: logs 1`] = `
{
  "logs": [
    "'to' option '.' determinated as 'dir'",
    "added './fixtures/file.txt' as a file dependency",
    "begin globbing './fixtures/file.txt'...",
    "created snapshot for './fixtures/file.txt'",
    "creating snapshot for './fixtures/file.txt'...",
    "determined './fixtures/file.txt' is a file",
    "determined that './fixtures/file.txt' should write to 'file.txt'",
    "finished to adding additional assets",
    "finished to process a pattern from 'file.txt' using './fixtures' context",
    "found './fixtures/file.txt'",
    "getting cache for './fixtures/file.txt'...",
    "getting stats for './fixtures/file.txt'...",
    "missed cache for './fixtures/file.txt'",
    "read './fixtures/file.txt'",
    "reading './fixtures/file.txt'...",
    "starting to add additional assets...",
    "starting to process a pattern from 'file.txt' using './fixtures' context",
    "stored cache for './fixtures/file.txt'",
    "storing cache for './fixtures/file.txt'...",
    "writing 'file.txt' from './fixtures/file.txt' to compilation assets...",
    "written 'file.txt' from './fixtures/file.txt' to compilation assets",
  ],
}
`;

exports[`CopyPlugin logging should logging when "from" is a glob: logs 1`] = `
{
  "logs": [
    "'to' option '.' determinated as 'dir'",
    "'to' option '.' determinated as 'dir'",
    "'to' option '.' determinated as 'dir'",
    "added './fixtures/directory' as a context dependency",
    "added './fixtures/directory/directoryfile.txt' as a file dependency",
    "added './fixtures/directory/nested/deep-nested/deepnested.txt' as a file dependency",
    "added './fixtures/directory/nested/nestedfile.txt' as a file dependency",
    "begin globbing './fixtures/directory/**'...",
    "created snapshot for './fixtures/directory/directoryfile.txt'",
    "created snapshot for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "created snapshot for './fixtures/directory/nested/nestedfile.txt'",
    "creating snapshot for './fixtures/directory/directoryfile.txt'...",
    "creating snapshot for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "creating snapshot for './fixtures/directory/nested/nestedfile.txt'...",
    "determined './fixtures/directory/**' is a glob",
    "determined that './fixtures/directory/directoryfile.txt' should write to 'directory/directoryfile.txt'",
    "determined that './fixtures/directory/nested/deep-nested/deepnested.txt' should write to 'directory/nested/deep-nested/deepnested.txt'",
    "determined that './fixtures/directory/nested/nestedfile.txt' should write to 'directory/nested/nestedfile.txt'",
    "finished to adding additional assets",
    "finished to process a pattern from 'directory/**' using './fixtures' context",
    "found './fixtures/directory/directoryfile.txt'",
    "found './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "found './fixtures/directory/nested/nestedfile.txt'",
    "getting cache for './fixtures/directory/directoryfile.txt'...",
    "getting cache for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "getting cache for './fixtures/directory/nested/nestedfile.txt'...",
    "getting stats for './fixtures/directory/**'...",
    "missed cache for './fixtures/directory/directoryfile.txt'",
    "missed cache for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "missed cache for './fixtures/directory/nested/nestedfile.txt'",
    "read './fixtures/directory/directoryfile.txt'",
    "read './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "read './fixtures/directory/nested/nestedfile.txt'",
    "reading './fixtures/directory/directoryfile.txt'...",
    "reading './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "reading './fixtures/directory/nested/nestedfile.txt'...",
    "starting to add additional assets...",
    "starting to process a pattern from 'directory/**' using './fixtures' context",
    "stored cache for './fixtures/directory/directoryfile.txt'",
    "stored cache for './fixtures/directory/nested/deep-nested/deepnested.txt'",
    "stored cache for './fixtures/directory/nested/nestedfile.txt'",
    "storing cache for './fixtures/directory/directoryfile.txt'...",
    "storing cache for './fixtures/directory/nested/deep-nested/deepnested.txt'...",
    "storing cache for './fixtures/directory/nested/nestedfile.txt'...",
    "writing 'directory/directoryfile.txt' from './fixtures/directory/directoryfile.txt' to compilation assets...",
    "writing 'directory/nested/deep-nested/deepnested.txt' from './fixtures/directory/nested/deep-nested/deepnested.txt' to compilation assets...",
    "writing 'directory/nested/nestedfile.txt' from './fixtures/directory/nested/nestedfile.txt' to compilation assets...",
    "written 'directory/directoryfile.txt' from './fixtures/directory/directoryfile.txt' to compilation assets",
    "written 'directory/nested/deep-nested/deepnested.txt' from './fixtures/directory/nested/deep-nested/deepnested.txt' to compilation assets",
    "written 'directory/nested/nestedfile.txt' from './fixtures/directory/nested/nestedfile.txt' to compilation assets",
  ],
}
`;

exports[`CopyPlugin logging should logging when 'to' is a function: logs 1`] = `
{
  "logs": [
    "'to' option 'newFile.txt' determinated as 'file'",
    "added './fixtures/file.txt' as a file dependency",
    "begin globbing './fixtures/file.txt'...",
    "created snapshot for './fixtures/file.txt'",
    "creating snapshot for './fixtures/file.txt'...",
    "determined './fixtures/file.txt' is a file",
    "determined that './fixtures/file.txt' should write to 'newFile.txt'",
    "finished to adding additional assets",
    "finished to process a pattern from 'file.txt' using './fixtures' context",
    "found './fixtures/file.txt'",
    "getting cache for './fixtures/file.txt'...",
    "getting stats for './fixtures/file.txt'...",
    "missed cache for './fixtures/file.txt'",
    "read './fixtures/file.txt'",
    "reading './fixtures/file.txt'...",
    "starting to add additional assets...",
    "starting to process a pattern from 'file.txt' using './fixtures' context",
    "stored cache for './fixtures/file.txt'",
    "storing cache for './fixtures/file.txt'...",
    "writing 'newFile.txt' from './fixtures/file.txt' to compilation assets...",
    "written 'newFile.txt' from './fixtures/file.txt' to compilation assets",
  ],
}
`;

exports[`CopyPlugin stats should work have assets info: assets 1`] = `
{
  ".dottedfile": "dottedfile contents
",
  "asset-modules/deepnested.txt": "",
  "directoryfile.txt": "new",
  "nested/deep-nested/deepnested.txt": "",
  "nested/nestedfile.txt": "",
}
`;

exports[`CopyPlugin stats should work have assets info: assets info 1`] = `
[
  {
    "info": {
      "copied": true,
      "immutable": undefined,
      "sourceFilename": "directory/.dottedfile",
    },
    "name": ".dottedfile",
  },
  {
    "info": {
      "copied": undefined,
      "immutable": undefined,
      "sourceFilename": "directory/nested/deep-nested/deepnested.txt",
    },
    "name": "asset-modules/deepnested.txt",
  },
  {
    "info": {
      "copied": true,
      "immutable": undefined,
      "sourceFilename": "directory/directoryfile.txt",
    },
    "name": "directoryfile.txt",
  },
  {
    "info": {
      "copied": undefined,
      "immutable": undefined,
      "sourceFilename": undefined,
    },
    "name": "main.js",
  },
  {
    "info": {
      "copied": true,
      "immutable": undefined,
      "sourceFilename": "directory/nested/deep-nested/deepnested.txt",
    },
    "name": "nested/deep-nested/deepnested.txt",
  },
  {
    "info": {
      "copied": true,
      "immutable": undefined,
      "sourceFilename": "directory/nested/nestedfile.txt",
    },
    "name": "nested/nestedfile.txt",
  },
]
`;

exports[`CopyPlugin stats should work have assets info: errors 1`] = `[]`;

exports[`CopyPlugin stats should work have assets info: warnings 1`] = `[]`;

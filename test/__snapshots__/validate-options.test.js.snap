// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`validate options should throw an error on the "options" option with "{"concurrency":true}" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.options.concurrency should be a number.
   -> Limits the number of simultaneous requests to fs.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#concurrency"
`;

exports[`validate options should throw an error on the "options" option with "{"unknown":true}" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.options has an unknown property 'unknown'. These properties are valid:
   object { concurrency? }"
`;

exports[`validate options should throw an error on the "patterns" option with "" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns should be an array:
   [non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }, ...] (should not have fewer than 1 item)"
`;

exports[`validate options should throw an error on the "patterns" option with "[""]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] should be a non-empty string."
`;

exports[`validate options should throw an error on the "patterns" option with "[]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns should be a non-empty array."
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"","to":"dir","context":"context","noErrorOnMissing":"true"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].noErrorOnMissing should be a boolean.
   -> Doesn't generate an error on missing file(s).
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#noerroronmissing"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"","to":"dir","context":"context"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].from should be a non-empty string.
   -> Glob or path from where we copy files.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#from"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"dir","info":"string"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] should be one of these:
   non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }
   Details:
    * options.patterns[0].info should be one of these:
      object { … } | function
      -> Allows to add assets info.
      -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#info
      Details:
       * options.patterns[0].info should be an object:
         object { … }
       * options.patterns[0].info should be an instance of function."
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"dir","info":true}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] should be one of these:
   non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }
   Details:
    * options.patterns[0].info should be one of these:
      object { … } | function
      -> Allows to add assets info.
      -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#info
      Details:
       * options.patterns[0].info should be an object:
         object { … }
       * options.patterns[0].info should be an instance of function."
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","filter":"test"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].filter should be an instance of function.
   -> Allows to filter copied assets.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#filter"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":"context","force":"true"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].force should be a boolean.
   -> Overwrites files already in 'compilation.assets' (usually added by other plugins/loaders).
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#force"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":"context","toType":"foo"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].toType should be one of these:
   "dir" | "file" | "template"
   -> Determinate what is to option - directory, file or template.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#totype"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":"context","transform":{"foo":"bar"}}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].transform has an unknown property 'foo'. These properties are valid:
   object { transformer?, cache? }"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":"context","transform":true}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] should be one of these:
   non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }
   Details:
    * options.patterns[0].transform should be one of these:
      function | object { transformer?, cache? }
      -> Allows to modify the file contents.
      -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#transform
      Details:
       * options.patterns[0].transform should be an instance of function.
       * options.patterns[0].transform should be an object:
         object { transformer?, cache? }"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":"context","transformAll":true}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].transformAll should be an instance of function.
   -> Allows you to modify the contents of multiple files and save the result to one file.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#transformall"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","context":true}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].context should be a string.
   -> A path that determines how to interpret the 'from' path.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#context"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","priority":"5"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].priority should be a number.
   -> Allows to specify the priority of copying files with the same destination name.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#priority"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir","priority":true}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].priority should be a number.
   -> Allows to specify the priority of copying files with the same destination name.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#priority"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":"dir"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].priority should be a number.
   -> Allows to specify the priority of copying files with the same destination name.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#priority"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":"test.txt","to":true,"context":"context"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] should be one of these:
   non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }
   Details:
    * options.patterns[0].to should be one of these:
      string | function
      -> Output path.
      -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#to
      Details:
       * options.patterns[0].to should be a string.
       * options.patterns[0].to should be an instance of function."
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":{"glob":"**/*","dot":false},"to":"dir","context":"context"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].from should be a non-empty string.
   -> Glob or path from where we copy files.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#from"
`;

exports[`validate options should throw an error on the "patterns" option with "[{"from":true,"to":"dir","context":"context"}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0].from should be a non-empty string.
   -> Glob or path from where we copy files.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#from"
`;

exports[`validate options should throw an error on the "patterns" option with "[{}]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns[0] misses the property 'from'. Should be:
   non-empty string
   -> Glob or path from where we copy files.
   -> Read more at https://github.com/webpack-contrib/copy-webpack-plugin#from"
`;

exports[`validate options should throw an error on the "patterns" option with "{}" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns should be an array:
   [non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }, ...] (should not have fewer than 1 item)"
`;

exports[`validate options should throw an error on the "patterns" option with "true" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns should be an array:
   [non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }, ...] (should not have fewer than 1 item)"
`;

exports[`validate options should throw an error on the "patterns" option with "true" value 2`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options.patterns should be an array:
   [non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }, ...] (should not have fewer than 1 item)"
`;

exports[`validate options should throw an error on the "patterns" option with "undefined" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options misses the property 'patterns'. Should be:
   [non-empty string | object { from, to?, context?, globOptions?, filter?, transformAll?, toType?, force?, priority?, info?, transform?, noErrorOnMissing? }, ...] (should not have fewer than 1 item)"
`;

exports[`validate options should throw an error on the "unknown" option with "/test/" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "[]" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "{"foo":"bar"}" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "{}" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "1" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "false" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "test" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;

exports[`validate options should throw an error on the "unknown" option with "true" value 1`] = `
"Invalid options object. Copy Plugin has been initialized using an options object that does not match the API schema.
 - options has an unknown property 'unknown'. These properties are valid:
   object { patterns, options? }"
`;
